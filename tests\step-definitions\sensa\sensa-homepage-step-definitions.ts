import { Then } from '@wdio/cucumber-framework';
import sensaHomepagePage from '../../pages/sensa/sensa-homepage.page.ts';
import logger from '../../support/utils/logger.util.ts';
import sensaRegistrationPageObject from '../../page-object/sensa/sensa-registration.pageObject.ts';

Then(/^The user Validates Homepage Page with filepath (.*) sheet name (.*) and scenario name (.*)$/, async function (filepath: string, sheetname: string, scenarioname: string) {
    await sensaHomepagePage.homepagevalidation(filepath, sheetname, scenarioname);
    logger.info('Validated Homepage Page Successfully');
});

Then(/^The user clicks on all buttons and verify the navigations from home page$/, async function () {
    await sensaHomepagePage.clickonallbuttonsandverifynavigationsinhomepage();
    logger.info('Verified Navigations Successfully');
    await expect(sensaRegistrationPageObject.lbllogo_sensa).toBeDisplayed();
    logger.info('Navigated to Home Page Successfully');
});