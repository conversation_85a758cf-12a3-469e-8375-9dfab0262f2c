import elementActions from '../../support/actions/element.actions.ts';
import sensaStorelocatorPage from '../../page-object/sensa/sensa-storelocator.page.ts';
import sensaFooterlinkPage from './sensa-footerlink.page.ts';
import logger from '../../support/utils/logger.util.ts';
import { el as _el } from '@faker-js/faker';



class Storlocator {


  public async navigateToStorelocatorPage() {
    try {
      const currentUrl = await browser.getUrl();
      if (currentUrl.includes('sensa')) {
        await elementActions.assertion(sensaStorelocatorPage.hamburgerMenu);
        await elementActions.click(sensaStorelocatorPage.hamburgerMenu);
        await elementActions.click(sensaStorelocatorPage.sensaStorelocator);
        await elementActions.waitForClickable(sensaStorelocatorPage.sensafindastore);
        await expect(sensaStorelocatorPage.sensafindastore).toBeDisplayed();
        await elementActions.assertion(sensaStorelocatorPage.sensafindastore);
      }
    } catch (error) {
      console.error('Error in navigateToStorelocatorPage:', error);
      throw error;
    }
  }



  public async usemylocation() {
    try {
      const currentUrl = await browser.getUrl();
      if (currentUrl.includes('sensa')) {
        await elementActions.assertion(sensaStorelocatorPage.sensaUsemyLocation);
        await elementActions.click(sensaStorelocatorPage.sensaUsemyLocation);
        await sensaFooterlinkPage.allowpopup();
        await elementActions.assertion(sensaStorelocatorPage.sensaZipcode);
        await elementActions.assertion(sensaStorelocatorPage.sensafindStores);
        // await elementActions.assertion(sensaStorelocatorPage.sensafilterbyproduct);
        // await elementActions.assertion(sensaStorelocatorPage.sensafilterbyproducttext);
      }
    } catch (error) {
      console.error('Error in usemylocation:', error);
      throw error;
    }
  }


  public async enterZipcode(Zipcode: string) {
    try {
      const currentUrl = await browser.getUrl();
      if (currentUrl.includes('sensa')) {
        await elementActions.assertion(sensaStorelocatorPage.sensaUsemyLocation);
        await elementActions.assertion(sensaStorelocatorPage.sensaZipcode);
        await elementActions.setValue(sensaStorelocatorPage.sensaZipcode, Zipcode);
        await elementActions.click(sensaStorelocatorPage.sensafindStores);
        await elementActions.assertion(sensaStorelocatorPage.sensafilterbyproduct);
        await elementActions.assertion(sensaStorelocatorPage.sensafilterbyproducttext);
      }
    } catch (error) {
      console.error('Error in enterZipcode:', error);
      throw error;
    }
  }


  public async validateAllStores() {
    try {
      for (let i = 1; i <= 3; i++) {
        await this.validateStoreCardByIndex(i);
      }
      console.log('Validated all initial stores');
    } catch (error) {
      logger.error('Failed to validate all stores', { error });
      throw error;
    }
  }

  public async validateLoadMoreStores() {
    try {
      await sensaStorelocatorPage.sensaloadMore.scrollIntoView();
      await elementActions.click(sensaStorelocatorPage.sensaloadMore);

      for (let i = 11; i <= 13; i++) {
        await this.validateStoreCardByIndex(i);
      }

      console.log('Validated Load More store results');
    } catch (error) {
      logger.error('Failed to validate Load More store results', { error });
      throw error;
    }
  }
  private async validateStoreCardByIndex(index: number) {
    const numberLocator = $(`(//*[@class="cmp-store-locator__store-list__stores-store__number"])[${index}]`);
    const nameLocator = $(`(//*[@class="cmp-store-locator__store-list__stores-store__headline"])[${index}]`);
    const addressLine1 = $(`((//*[@class="cmp-store-locator__store-list__stores-store__headline"])//following::p[1])[${index}]`);
    const addressLine2 = $(`((//*[@class="cmp-store-locator__store-list__stores-store__headline"])//following::p[2])[${index}]`);
    const contactNumber = $(`((//*[@class="cmp-store-locator__store-list__stores-store__headline"])//following::p[3])[${index}]`);
    const directionLink = $(`(//a[contains(text(),"Get Directions")])[${index}]`);
    const distanceLocator = $(`(//*[@class="cmp-store-locator__store-list__stores-store__distance"])[${index}]`);

    const locators = [
      { name: 'Store Number', locator: numberLocator },
      { name: 'Store Name', locator: nameLocator },
      { name: 'Address Line 1', locator: addressLine1 },
      { name: 'Address Line 2', locator: addressLine2 },
      { name: 'Contact Number', locator: contactNumber },
      { name: 'Get Directions Link', locator: directionLink },
      { name: 'Distance', locator: distanceLocator },
    ];

    for (const { name, locator } of locators) {
      const isDisplayed = await locator.isDisplayed().catch(() => false);
      if (isDisplayed) {
        await elementActions.assertion(locator);
        console.log(`${name} is displayed for index ${index}`);
      } else {
        console.warn(`${name} is NOT displayed for index ${index}`);
      }
    }
  }

  public async ClickonFilterbyProductvalidateAll6ProductsFromFilterByProduct() {
    try {
      // Click on "Filter by Product"
      await elementActions.click(sensaStorelocatorPage.sensafilterbyproduct);
      await browser.pause(3000);

      for (let i = 1; i <= 6; i++) {
        // Dynamic XPath for product image
        const product = await $(`(//img[@class='cmp-store-locator__filter__img'])[${i}]`);
        const productName = await product.getAttribute('alt');
        console.log(`Product ${i}: ${productName}`);

        await elementActions.click(product);
        await browser.pause(3000);

        await elementActions.assertion(sensaStorelocatorPage.btnApplyFilter);
        await elementActions.click(sensaStorelocatorPage.btnApplyFilter);
        await browser.pause(2000);

        // const clearFilter = await $('(//*[text()="Clear Filter"])[2]');
        // const isClearFilterDisplayed = await clearFilter.isDisplayed().catch(() => false);

        if ((await sensaStorelocatorPage.clearFilter.isDisplayed())) {
          await elementActions.click(sensaStorelocatorPage.clearFilter);
          await elementActions.assertion(sensaStorelocatorPage.btnfilterproduct);
          await elementActions.click(sensaStorelocatorPage.btnfilterproduct);
        } else {
          await elementActions.waitForDisplayed(sensaStorelocatorPage.labelProduct);
          await elementActions.assertion(sensaStorelocatorPage.labelProduct);
          await browser.pause(2000);
          await elementActions.assertion(sensaStorelocatorPage.sensafilterbyproducttext);
          await elementActions.assertion(sensaStorelocatorPage.firstStoreNumber_Stores);
          await elementActions.click(sensaStorelocatorPage.firstStoreNumber_Stores);
          await elementActions.assertion(sensaStorelocatorPage.firstStoreHeader_Stores);

          await elementActions.assertion(sensaStorelocatorPage.btnfilterproduct);
          await elementActions.click(sensaStorelocatorPage.btnfilterproduct);
        }
      }

      await elementActions.assertion(sensaStorelocatorPage.closebutton);
      await elementActions.click(sensaStorelocatorPage.closebutton);
      console.log(' Successfully validated all 6 products from filter by product');
    } catch (error) {
      logger.error(' Failed to validate all 6 products from filter by product:', { error });
      throw error;
    }
  }
}

export default new Storlocator();