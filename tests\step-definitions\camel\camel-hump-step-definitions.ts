import { Then, When } from '@wdio/cucumber-framework';
import camelHumpPage from '../../pages/camel/camel-hump.page.ts';
import { call as _call } from '@google-cloud/vision/build/src/helpers.js';

When(/^The user clicks on Hump$/, async function () {
    await camelHumpPage.humpPage();
});

Then(/^The user Validates Hump Page with filepath (.*) sheet name (.*) and scenario name (.*)$/, async function (filepath: string, sheetname: string, scenarioname: string) {
    await camelHumpPage.humpPageValidation(filepath, sheetname, scenarioname);
    await camelHumpPage.clickoneachlinkandVerify();
});

Then(/^The user clicks on anyone button on each link and enters valid comments as (.*) and verify last comment timestap as (.*)$/, async function (comment:string, timestamp:string) {
    await camelHumpPage.clickoneachbuttonandVerifycomments(comment,timestamp);
});
