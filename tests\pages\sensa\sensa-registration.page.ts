import logger from '../../support/utils/logger.util.ts';
import elementActions from '../../support/actions/element.actions.ts';
import RegistrationPageObject from '../../page-object/sensa/sensa-registration.pageObject.ts';
import { faker } from '@faker-js/faker';
import path from 'path';
import sensaAccountPage from './sensa-account.page.ts';
import sensaForgotusernamePageObject from '../../page-object/sensa/sensa-forgotusername.pageObject.ts';
import { JsonTestDataHandler } from '../../support/utils/JsonTestDataHandler.ts';
import sensaAccountPageObject from '../../page-object/sensa/sensa-account.pageObject.ts';
import sensaRegistrationPageObject from '../../page-object/sensa/sensa-registration.pageObject.ts';
import assertionHelper from '../../support/helpers/assertion-helper.ts';
import camelRegistrationPageObject from '../../page-object/camel/camel-registration.pageObject.ts';
import { VeratadTestDataUtil } from '../../support/utils/veratadTestData.util.ts';
import browserActions from '../../support/actions/browser.actions.ts';
const testData = await VeratadTestDataUtil.generateAndSubmit();

class Registration {


  async navigateToUrl(url: string) {
    try {
      await browser.waitUntil(async () => true, { timeout: 5000 });
      await browserActions.navigateTo(url);
      await elementActions.assertion(RegistrationPageObject.lblsignIn_sensa);
      console.log('Navigated to: ${url}');
    } catch (error) {
      logger.error('Failed to Launch URL', { error });
      throw error;
    }
  }

  async clickonjoinNowButton() {
    try {
      const firstname = faker.person.firstName();
      const lastName = faker.person.lastName();
      const randomNumber = String(Math.floor(Math.random() * (9999 - 1001 + 1)) + 1001); // Generate a random number between 1001 and 999
      const email = `rjrautomationtest+${firstname}${randomNumber}${lastName}@gmail.com`;
      await elementActions.setValue(RegistrationPageObject.txtregemail_sensa, email);
      await elementActions.click(RegistrationPageObject.txtjoinnow_sensa);
      await elementActions.assertion(RegistrationPageObject.lblregistrationPage_sensa);
      console.log('Clicked on Join Now Button');
    } catch (error) {
      logger.error('Unable to Click on Join Now Button', { error });
      throw error;
    }
  }

  async enterDetailsintellUsaboutpage() {
    try {
      const dob = testData.submittedData.dob;
      const [year, month, day] = [dob.slice(0, 4), dob.slice(4, 6), dob.slice(6, 8)];
      console.log('Month', month);
      console.log('Year', year);
      console.log('Day', day);
      console.log('lastname', testData.submittedData.ln);

      await elementActions.waitForDisplayed(RegistrationPageObject.txtFirstName_sensa);
      await elementActions.setValue(RegistrationPageObject.txtFirstName_sensa, testData.submittedData.fn);
      await elementActions.setValue(RegistrationPageObject.txtLastName_sensa, testData.submittedData.ln);
      await elementActions.setValue(RegistrationPageObject.txtaddress_sensa, testData.submittedData.addr);
      await elementActions.setValue(RegistrationPageObject.txtcity_sensa, testData.submittedData.city);
      await elementActions.setValue(RegistrationPageObject.txtzipCode_sensa, testData.submittedData.zip);
      await elementActions.setValue(RegistrationPageObject.txtregemail_sensa, testData.submittedData.email);
      const monthdd = await RegistrationPageObject.txtmonth_sensa;
      await monthdd.selectByVisibleText(month);
      const daydd = await RegistrationPageObject.txtday_sensa;
      await daydd.selectByVisibleText(day);
      const yeardd = await RegistrationPageObject.txtyear_sensa;
      await yeardd.selectByVisibleText(year);
      await $('body').click();
      await elementActions.clickusingJavascript(RegistrationPageObject.txtweCertify_sensa);
      await elementActions.clickusingJavascript(RegistrationPageObject.txtcontinuetoNextStep_sensa);
      await elementActions.assertion(RegistrationPageObject.lblaccountSetup_sensa);
      console.log('Entered Details Successfully');
    } catch (error) {
      logger.error('Unable to enter details', { error });
      throw error;
    }
  }

  async enterDetailsinaccountSetupPage(password: string, Cpassword: string, securityquestion: string, securityanswer: string) {
    try {
      await (await RegistrationPageObject.txtpassword_sensa).addValue(password);
      await (await RegistrationPageObject.txtconfirmPassword_sensa).addValue(Cpassword);
      await (await RegistrationPageObject.txtsecurityQuestion_sensa).selectByVisibleText(securityquestion);
      const textbox = await RegistrationPageObject.txtsecuritynswer_sensa;
      await textbox.setValue(securityanswer);
      await browser.execute((_selector) => {
        const el = document.querySelector('#regSecurityAnswer') as HTMLInputElement;
        if (el) {
          el.dispatchEvent(new Event('change', { bubbles: true }));
        }
      }, await RegistrationPageObject.txtsecuritynswer_sensa);
      await elementActions.clickusingJavascript(RegistrationPageObject.txtcontinuetonextStep_sensa);
      console.log('Entered Details Successfully');
    } catch (error) {
      logger.error('Unable to enter details', { error });
      throw error;
    }
  }
  async enterMobileNumberinverifyIdentityPage(mobileNumber: string) {
    try {
      // Wait for page to load instead of using pause
      await browser.waitUntil(async () => {
        const contexts = await browser.getContexts();
        return contexts.length > 0;
      }, { timeout: 5000, timeoutMsg: 'Failed to load page contexts' });
      const url = driver.getUrl();
      console.log(await browser.getContext());
      await browser.switchContext('NATIVE_APP');
      if (driver.isIOS) {
        await (await RegistrationPageObject.txtphone_sensa_iOS).setValue(mobileNumber);
        if ((await url).includes('sensa')) {
          await (await RegistrationPageObject.btnSendMeCode_sensa_iOS).click();
          const sendme = RegistrationPageObject.btnSendMeCode_sensa_iOS;
          const isDisplayed = await sendme.isDisplayed();
          if (isDisplayed) {
            await (await RegistrationPageObject.btnSendMeCode_sensa_iOS).click();
          }
        } else {
          await (await camelRegistrationPageObject.btnSendMeCode_camel_iOS).click();
        }
        const contexts = await driver.getContexts();
        const webviewContext = contexts.find(ctx => typeof ctx === 'string' && ctx.startsWith('WEBVIEW'));
        if (webviewContext) {
          await driver.switchContext(webviewContext as string);
        }
      } else {
        await (await RegistrationPageObject.txtphone_sensa).setValue(mobileNumber);
        await (await RegistrationPageObject.btnsendmeCode_sensa).click();
        const sendme = RegistrationPageObject.btnsendmeCode_sensa;
        const isDisplayed = await sendme.isDisplayed();
        if (isDisplayed) {
          await (await RegistrationPageObject.btnsendmeCode_sensa).click();
        }
        await browser.switchContext('CHROMIUM');
      }

      console.log('Entered Mobile Number Successfully');
    } catch (error) {
      logger.error('Unable to Enter Mobile Number', { error });
      throw error;
    }
  }

  async enterSSNinverifyssnPage() {
    try {
      // Wait for page to load instead of using pause
      await browser.waitUntil(async () => {
        const url = await browser.getUrl();
        return url.length > 0;
      }, { timeout: 5000, timeoutMsg: 'Failed to load page URL' });
      const url = await browser.getUrl();
      const ssnnumber = await testData.submittedData.ssn;
      console.log('ssn is:', ssnnumber);
      const last4digits = ssnnumber.slice(-4);
      console.log('4didgit is:', last4digits);
      await browser.switchContext('NATIVE_APP');
      if (driver.isIOS) {
        if ((await url).includes('camel')) {
          await (await camelRegistrationPageObject.txtssn_camel).setValue(last4digits);
          await assertionHelper.assertElementEnabled(camelRegistrationPageObject.btnverifyMe_camel_iOS);
          await (await camelRegistrationPageObject.btnverifyMe_camel_iOS).click();
        } else {
          await (await RegistrationPageObject.txtphone_sensa_iOS).click();
          await (await RegistrationPageObject.txtphone_sensa_iOS).setValue(last4digits);
          await (await RegistrationPageObject.btnverifyMe_sensa_iOS).click();
        }
        const contexts = await driver.getContexts();
        const webviewContext = contexts.find(ctx => typeof ctx === 'string' && ctx.startsWith('WEBVIEW'));
        if (webviewContext) {
          await driver.switchContext(webviewContext as string);
        }
      } else {
        await this.humanLikeTyping(browser, RegistrationPageObject.txtphone_sensa, last4digits);
        await (await RegistrationPageObject.btnverifyMe_sensa).click();
        await browser.switchContext('CHROMIUM');
      }
      await browser.waitUntil(async () => true, { timeout: 5000 });
      console.log('Entered SSN Successfully');
    } catch (error) {
      logger.error('Unable to Enter SSN', { error });
      throw error;
    }
  }

  async humanLikeTyping(driver: WebdriverIO.Browser, element: ChainablePromiseElement, text: string) {
    if (driver.isIOS) {
      await element.click();
    } else {
      await element.doubleClick();
    }
    for (const char of text) {
      const delay = Math.floor(Math.random() * 150) + 50;
      await driver.performActions([{
        type: 'key',
        id: 'keyboard',
        actions: [
          { type: 'keyDown', value: char },
          { type: 'pause', duration: delay },
          { type: 'keyUp', value: char },
        ],
      }]);
      await driver.pause(delay);
    }
    await driver.releaseActions();
  }


  async navigatetoSignupPageandClickOnNoThanksLink() {
    try {
      await elementActions.waitForDisplayed(RegistrationPageObject.lnknoThanks_sensa);
      await elementActions.click(RegistrationPageObject.lnknoThanks_sensa);
      await elementActions.assertion(RegistrationPageObject.lblcongratulations_sensa);
      console.log('Clicked on No thanks link Successfully');
    } catch (error) {
      logger.error('Unable to Click on No thanks link ', { error });
      throw error;
    }
  }
  async navigateTocongratulationsPageandClickOnTakemeToSiteLink() {
    try {
      await elementActions.waitForDisplayed(RegistrationPageObject.lnktakemetosite_sensa);
      await elementActions.click(RegistrationPageObject.lnktakemetosite_sensa);
      console.log('User is Navigated to Home Page Successfully');
    } catch (error) {
      logger.error('Unable to Navigate to Home Page ', { error });
      throw error;
    }
  }

  async NavigatehomepageandverifySensaLogo() {
    try {
      await browser.waitUntil(async () => (await browser.execute(() => document.readyState)) === 'complete', {
        timeout: 10000,
        timeoutMsg: 'Home Page did not load in time',
      });
      await elementActions.assertion(RegistrationPageObject.lbllogo_sensa);
      console.log('User Logged in Successfully');
    } catch (error) {
      logger.error('Unable to Log in ', { error });
      throw error;
    }
  }

  async successfullLogoutfromSensasite() {
    try {
      const signin = RegistrationPageObject.lblsignIn_sensa;
      const logout = sensaAccountPageObject.lnklogout_sensa;
      let tries = 0;
      const maxTries = 4;

      // Check if user is already logged out
      if (await signin.isDisplayed()) {
        console.log('User is already logged out');
        return;
      }

      // Attempt logout until sign-in button is displayed or max tries reached
      while (!(await signin.isDisplayed()) && tries < maxTries) {
        try {
          // Navigate to account page and perform logout
          await sensaAccountPage.clickonaccountlinkfromheader();
          await elementActions.waitForDisplayed(logout);
          await elementActions.clickusingJavascript(logout);

          // Wait for logout to process by checking if sign-in appears
          await browser.waitUntil(
            async () => await signin.isDisplayed(),
            { timeout: 5000, timeoutMsg: 'Sign-in button did not appear after logout attempt' },
          ).catch(() => {
            // Continue if timeout - will be handled in the next check
          });

          // Check if logout was successful
          if (await signin.isDisplayed()) {
            console.log(`Logout successful on attempt ${tries + 1}`);
            break;
          }
        } catch (attemptError) {
          logger.warn(`Logout attempt ${tries + 1} failed:`, { error: attemptError });
        }

        tries++;
      }

      // Final verification that logout was successful
      if (!(await signin.isDisplayed())) {
        throw new Error(`Failed to logout after ${maxTries} attempts`);
      }

      await elementActions.waitForDisplayed(RegistrationPageObject.lblsignIn_sensa);
      await elementActions.assertion(RegistrationPageObject.lblsignIn_sensa);
      console.log('User Logged out Successfully');
    } catch (error) {
      logger.error('Unable to Log out Successfully ', { error });
      throw error;
    }
  }

  async entersEMailinemailField(email: string) {
    try {
      await elementActions.setValue(RegistrationPageObject.txtregemail_sensa, email);
      await elementActions.click(RegistrationPageObject.txtjoinnow_sensa);
      console.log('Email is entered Successfully');
    } catch (error) {
      logger.error('Unable to CEnter Email', { error });
      throw error;
    }
  }

  async verifyRegistrationerrorinLoginPage(mssg: string) {
    try {

      await elementActions.assertion(RegistrationPageObject.lblsignIn_sensa);
      await sensaAccountPage.mssgcomparision(RegistrationPageObject.lblregistrationError_sensa, mssg);
      console.log('Validated Registration Error');
    } catch (error) {
      logger.error('Unable to Validate Registration Error', { error });
      throw error;
    }
  }

  async verifyifaccountexistandvalidatemessage(mssg: string) {
    try {

      await elementActions.assertion(RegistrationPageObject.lblsignIn_sensa);
      await sensaAccountPage.mssgcomparision(RegistrationPageObject.lblregistrationsameBrand_sensa, mssg);
      console.log('Account Already Exist');
    } catch (error) {
      logger.error('Unable to Verify Account', { error });
      throw error;
    }
  }

  async tellUsaboutpageValidationinRegistration(filename: string, sheetname: string, scenarioname: string) {
    try {

      const SHEET_NAME = sheetname;
      const jsonFilePath = path.join(process.cwd(), 'data', filename);
      const testData = new JsonTestDataHandler(jsonFilePath);
      const userData1 = testData.getTestCaseData(SHEET_NAME, scenarioname);
      console.log(`User Data for TC01: ${JSON.stringify(userData1)}`);
      const hdrregistation = testData.getCellValue(SHEET_NAME, scenarioname, 'hdrTellus');
      const lblallfields = testData.getCellValue(SHEET_NAME, scenarioname, 'lblallfieldsnoted');
      const lbllegalname = testData.getCellValue(SHEET_NAME, scenarioname, 'lbllegalname');
      const lblgovtid = testData.getCellValue(SHEET_NAME, scenarioname, 'lblgovtid');
      const lblcurrentaddr = testData.getCellValue(SHEET_NAME, scenarioname, 'lblcurrentaddress');
      const lblfirstName = testData.getCellValue(SHEET_NAME, scenarioname, 'lblfirstname');
      const lbllastName = testData.getCellValue(SHEET_NAME, scenarioname, 'lbllastname');
      const lblstreetaddr = testData.getCellValue(SHEET_NAME, scenarioname, 'lblstreetaddress');
      const lblzipcode = testData.getCellValue(SHEET_NAME, scenarioname, 'lblzipcode');
      const lblcity = testData.getCellValue(SHEET_NAME, scenarioname, 'lblcity');
      const lbldob = testData.getCellValue(SHEET_NAME, scenarioname, 'lbldob');
      const lblstate = testData.getCellValue(SHEET_NAME, scenarioname, 'lblstate');
      const hdremail = testData.getCellValue(SHEET_NAME, scenarioname, 'hdrEmail');
      const lblyouremail = testData.getCellValue(SHEET_NAME, scenarioname, 'lblyouremailaddr');
      const lblemail = testData.getCellValue(SHEET_NAME, scenarioname, 'lblEmail');
      const lblmonth = testData.getCellValue(SHEET_NAME, scenarioname, 'lblmonth');
      const lbldate = testData.getCellValue(SHEET_NAME, scenarioname, 'lbldate');
      const lblyear = testData.getCellValue(SHEET_NAME, scenarioname, 'lblyear');
      const lblcertify = testData.getCellValue(SHEET_NAME, scenarioname, 'lblCertify');
      await sensaAccountPage.mssgcomparision(sensaRegistrationPageObject.lbltellusabout_sensa, hdrregistation);
      await sensaAccountPage.mssgcomparision(sensaForgotusernamePageObject.lblallfields_sensa, lblallfields);
      await sensaAccountPage.mssgcomparision(sensaAccountPageObject.lbllegalName_sensa, lbllegalname);
      await sensaAccountPage.mssgcomparision(sensaAccountPageObject.lblgovtissue_sensa, lblgovtid);
      await elementActions.click(sensaAccountPageObject.txtfirstName_sensa);
      await sensaAccountPage.mssgcomparision(sensaAccountPageObject.lblfirstName_sensa, lblfirstName);
      await elementActions.click(sensaAccountPageObject.txtlastName_sensa);
      await sensaAccountPage.mssgcomparision(sensaAccountPageObject.lbllastName_sensa, lbllastName);
      await sensaAccountPage.mssgcomparision(sensaAccountPageObject.lblcurrentAddr_sensa, lblcurrentaddr);
      await elementActions.click(sensaAccountPageObject.txtcurrentAddr_sensa);
      await sensaAccountPage.mssgcomparision(sensaAccountPageObject.lblstretaddr_sensa, lblstreetaddr);
      await elementActions.click(sensaAccountPageObject.txtzipCode_sensa);
      await sensaAccountPage.mssgcomparision(sensaAccountPageObject.lblzipcode_sensa, lblzipcode);
      await elementActions.click(sensaAccountPageObject.txtaptOpt_sensa);
      await elementActions.click(sensaAccountPageObject.txtcity_sensa);
      await sensaAccountPage.mssgcomparision(sensaAccountPageObject.lblcity_sensa, lblcity);
      await elementActions.click(sensaAccountPageObject.txtstate_sensa);
      await sensaAccountPage.mssgcomparision(sensaAccountPageObject.lblstate_sensa, lblstate);
      await sensaAccountPage.mssgcomparision(sensaRegistrationPageObject.hdremail_sensa, hdremail);
      await sensaAccountPage.mssgcomparision(sensaRegistrationPageObject.lblyouemail_sensa, lblyouremail);
      await elementActions.click(RegistrationPageObject.txtregemail_sensa);
      await sensaAccountPage.mssgcomparision(sensaRegistrationPageObject.lblemail_sensa, lblemail);
      await sensaAccountPage.mssgcomparision(sensaAccountPageObject.lbldob_sensa, lbldob);
      await elementActions.click(RegistrationPageObject.txtmonth_sensa);
      await sensaAccountPage.mssgcomparision(sensaAccountPageObject.lblmonth_sensa, lblmonth);
      await elementActions.click(RegistrationPageObject.txtday_sensa);
      await sensaAccountPage.mssgcomparision(sensaAccountPageObject.lbldate_sensa, lbldate);
      await elementActions.click(RegistrationPageObject.txtyear_sensa);
      await sensaAccountPage.mssgcomparision(sensaAccountPageObject.lblyear_sensa, lblyear);
      await sensaAccountPage.mssgcomparision(RegistrationPageObject.txtweCertify_sensa, lblcertify);
      await elementActions.assertion(sensaRegistrationPageObject.txtcontinuetoNextStep_sensa);
      console.log('Validated the Content in Tellus about page Successfully');
    } catch (error) {
      logger.error('Failed to Validate the Content in Tellus about page', { error });
      throw error;
    }
  }


  async accountsetupPagevalidation(filename: string, sheetname: string, scenarioname: string) {
    try {

      const SHEET_NAME = sheetname;
      const jsonFilePath = path.join(process.cwd(), 'data', filename);
      const testData = new JsonTestDataHandler(jsonFilePath);
      const userData1 = testData.getTestCaseData(SHEET_NAME, scenarioname);
      console.log(`User Data for TC01: ${JSON.stringify(userData1)}`);
      const hdraccountsetup = testData.getCellValue(SHEET_NAME, scenarioname, 'ttlaccountSetup');
      const lblallfields = testData.getCellValue(SHEET_NAME, scenarioname, 'lblallfieldsrequire');
      const hdrusername = testData.getCellValue(SHEET_NAME, scenarioname, 'lblusername');
      const lblyourusername = testData.getCellValue(SHEET_NAME, scenarioname, 'lblemailaddr');
      await sensaAccountPage.mssgcomparision(sensaRegistrationPageObject.lblaccountSetup_sensa, hdraccountsetup);
      await sensaAccountPage.mssgcomparision(sensaRegistrationPageObject.lblallfields_sensa, lblallfields);
      await sensaAccountPage.mssgcomparision(sensaRegistrationPageObject.lblusername_sensa, hdrusername);
      await sensaAccountPage.mssgcomparision(sensaRegistrationPageObject.lblyourusername_sensa, lblyourusername);
      await elementActions.assertion(sensaRegistrationPageObject.txtusername_sensa);
      const hdrpassword = testData.getCellValue(SHEET_NAME, scenarioname, 'ttlPassword');
      const lblchooseapassword = testData.getCellValue(SHEET_NAME, scenarioname, 'lblchooseapassword');
      const lblpasswordCondition1 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblpasswordCondition1');
      const lblpasswordCondition2 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblpasswordCondition2');
      const lblpasswordCondition3 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblpasswordCondition3');
      const lblpasswordCondition4 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblpasswordCondition4');
      const lblpassword = testData.getCellValue(SHEET_NAME, scenarioname, 'lblpassword');
      const lblcpassword = testData.getCellValue(SHEET_NAME, scenarioname, 'lblconfirmpassword');
      await sensaAccountPage.mssgcomparision(sensaRegistrationPageObject.hdrPassword_sensa, hdrpassword);
      await sensaAccountPage.mssgcomparision(sensaRegistrationPageObject.lblpleasechoose_sensa, lblchooseapassword);
      await sensaAccountPage.mssgcomparision(sensaRegistrationPageObject.lblpasswordCondition1_sensa, lblpasswordCondition1);
      await sensaAccountPage.mssgcomparision(sensaRegistrationPageObject.lblpasswordCondition2_sensa, lblpasswordCondition2);
      await sensaAccountPage.mssgcomparision(sensaRegistrationPageObject.lblpasswordCondition3_sensa, lblpasswordCondition3);
      await sensaAccountPage.mssgcomparision(sensaRegistrationPageObject.lblpasswordCondition4_sensa, lblpasswordCondition4);
      await elementActions.click(RegistrationPageObject.txtpassword_sensa);
      await sensaAccountPage.mssgcomparision(sensaRegistrationPageObject.lblpasswordlabel_sensa, lblpassword);
      await elementActions.click(RegistrationPageObject.txtconfirmPassword_sensa);
      await sensaAccountPage.mssgcomparision(sensaRegistrationPageObject.lblcpasswordlabel_sensa, lblcpassword);
      await elementActions.assertion(RegistrationPageObject.txtpassword_sensa);
      await elementActions.assertion(RegistrationPageObject.txtconfirmPassword_sensa);
      const hdrsecurity = testData.getCellValue(SHEET_NAME, scenarioname, 'ttlsecurityquestion');
      const lblsecuritycondition = testData.getCellValue(SHEET_NAME, scenarioname, 'lblsequirtquestioncasesensitive');
      const lblanswer = testData.getCellValue(SHEET_NAME, scenarioname, 'lblanswer');
      await sensaAccountPage.mssgcomparision(sensaRegistrationPageObject.hdrSecurity_sensa, hdrsecurity);
      await sensaAccountPage.mssgcomparision(sensaRegistrationPageObject.lblsecurityanswercase_sensa, lblsecuritycondition);
      await elementActions.click(RegistrationPageObject.txtsecuritynswer_sensa);
      await sensaAccountPage.mssgcomparision(sensaRegistrationPageObject.lblanswerlabel_sensa, lblanswer);
      await sensaAccountPage.dropdownselection(sensaRegistrationPageObject.txtsecurityQuestion_sensa);
      await elementActions.assertion(sensaRegistrationPageObject.txtsecuritynswer_sensa);
      await elementActions.assertion(sensaRegistrationPageObject.txtcontinuetonextStep_sensa);

      console.log('Validated the Content in Account Setup page Successfully');
    } catch (error) {
      logger.error('Failed to Validate the Content in Account Setup page', { error });
      throw error;
    }
  }
  async verifyssnpageValidation(filename: string, sheetname: string, scenarioname: string) {
    try {

      await browser.waitUntil(async () => true, { timeout: 5000 });
      if (driver.isIOS) {
        console.log(await browser.getContexts());
        await browser.switchContext('NATIVE_APP');
        const SHEET_NAME = sheetname;
        const jsonFilePath = path.join(process.cwd(), 'data', filename);
        const testData = new JsonTestDataHandler(jsonFilePath);
        const userData1 = testData.getTestCaseData(SHEET_NAME, scenarioname);
        console.log(`User Data for TC01: ${JSON.stringify(userData1)}`);
        const hdroops = testData.getCellValue(SHEET_NAME, scenarioname, 'ttlssn');
        const lblssn = testData.getCellValue(SHEET_NAME, scenarioname, 'lbllastssn');
        await sensaAccountPage.mssgcomparision(sensaRegistrationPageObject.lbloopstry, hdroops);
        await sensaAccountPage.mssgcomparision(sensaRegistrationPageObject.lblssn_sensa, lblssn);
        await elementActions.assertion(RegistrationPageObject.txtphone_sensa_iOS);
        await elementActions.assertion(sensaRegistrationPageObject.btnverifyMe_sensa_iOS);
        const contexts = await driver.getContexts();
        const webviewContext = contexts.find(ctx => typeof ctx === 'string' && ctx.startsWith('WEBVIEW'));
        if (webviewContext) {
          await driver.switchContext(webviewContext as string);
        }
      }
      console.log('Verfied SSN page Successfully');
    } catch (error) {
      logger.error('Unable to verify SSN Page', { error });
      throw error;
    }
  }

  async signuppageValidation(filename: string, sheetname: string, scenarioname: string) {
    try {
      const SHEET_NAME = sheetname;
      const jsonFilePath = path.join(process.cwd(), 'data', filename);
      const testData = new JsonTestDataHandler(jsonFilePath);
      const userData1 = testData.getTestCaseData(SHEET_NAME, scenarioname);
      console.log(`User Data for TC01: ${JSON.stringify(userData1)}`);
      const hdrsignup = testData.getCellValue(SHEET_NAME, scenarioname, 'hdrsignup');
      const lbljoinsensa = testData.getCellValue(SHEET_NAME, scenarioname, 'lbljoinsensa');
      const hdrmobile = testData.getCellValue(SHEET_NAME, scenarioname, 'hdrmobile');
      const lblmobile = testData.getCellValue(SHEET_NAME, scenarioname, 'lblmobile');
      const lblcertifyinsignup = testData.getCellValue(SHEET_NAME, scenarioname, 'lblcertifyinsignup');
      await sensaAccountPage.mssgcomparision(sensaRegistrationPageObject.lblsignup_sensa, hdrsignup);
      await sensaAccountPage.mssgcomparision(sensaRegistrationPageObject.lbljoinsensa, lbljoinsensa);
      await sensaAccountPage.mssgcomparision(sensaRegistrationPageObject.hdrmobilenumber_sensa, hdrmobile);
      await elementActions.click(sensaRegistrationPageObject.txtmobilenumber_sensa);
      await sensaAccountPage.mssgcomparision(sensaRegistrationPageObject.lblmobilelabel_sensa, lblmobile);
      await sensaAccountPage.mssgcomparision(sensaRegistrationPageObject.lblicertify_sensa, lblcertifyinsignup);
      await elementActions.assertion(sensaRegistrationPageObject.btnsubscribe_sensa);
      await elementActions.assertion(sensaRegistrationPageObject.lnknoThanks_sensa);

      console.log('Validated the Content in Signup page Successfully');
    } catch (error) {
      logger.error('Failed to Validate the Content in Signup page', { error });
      throw error;
    }
  }

  async tellUsaboutpageAllErrorsvalidation(filename: string, sheetname: string, scenarioname: string) {
    try {

      const SHEET_NAME = sheetname;
      const jsonFilePath = path.join(process.cwd(), 'data', filename);
      const testData = new JsonTestDataHandler(jsonFilePath);
      const userData1 = testData.getTestCaseData(SHEET_NAME, scenarioname);
      console.log(`User Data for TC01: ${JSON.stringify(userData1)}`);
      const lblfirstName = testData.getCellValue(SHEET_NAME, scenarioname, 'lblerrorfirstname');
      const lbllastName = testData.getCellValue(SHEET_NAME, scenarioname, 'lblerrorlastname');
      const lblstreetaddr = testData.getCellValue(SHEET_NAME, scenarioname, 'lblerrorstreetaddr');
      const lblzipcode = testData.getCellValue(SHEET_NAME, scenarioname, 'lblerrorzipcode');
      const lblcity = testData.getCellValue(SHEET_NAME, scenarioname, 'lblerrorcity');
      const lblstate = testData.getCellValue(SHEET_NAME, scenarioname, 'lblerrorstate');
      const lblemail = testData.getCellValue(SHEET_NAME, scenarioname, 'lblerroremail');
      const lblmonth = testData.getCellValue(SHEET_NAME, scenarioname, 'lblerrormonth');
      const lbldate = testData.getCellValue(SHEET_NAME, scenarioname, 'lblerrordate');
      const lblyear = testData.getCellValue(SHEET_NAME, scenarioname, 'lblerroryear');
      if (driver.isIOS) {
        await elementActions.click(sensaAccountPageObject.txtfirstName_sensa);
        await elementActions.click(sensaAccountPageObject.txtlastName_sensa);
      } else {
        await sensaAccountPageObject.txtfirstName_sensa.doubleClick();
        await sensaAccountPageObject.txtlastName_sensa.doubleClick();
      }
      await sensaAccountPage.mssgcomparision(sensaForgotusernamePageObject.lblerrorFirstName_sensa, lblfirstName);
      if (driver.isIOS) {
        await elementActions.click(sensaAccountPageObject.txtlastName_sensa);
        await elementActions.click(sensaAccountPageObject.txtfirstName_sensa);
      } else {
        await sensaAccountPageObject.txtlastName_sensa.doubleClick();
        await sensaAccountPageObject.txtfirstName_sensa.doubleClick();
      }
      await sensaAccountPage.mssgcomparision(sensaForgotusernamePageObject.lblerrorLastName_sensa, lbllastName);
      if (driver.isIOS) {
        await elementActions.click(sensaAccountPageObject.txtcurrentAddr_sensa);
        await elementActions.click(sensaAccountPageObject.txtfirstName_sensa);
      } else {
        await sensaAccountPageObject.txtcurrentAddr_sensa.doubleClick();
        await sensaAccountPageObject.txtfirstName_sensa.doubleClick();
      }
      await sensaAccountPage.mssgcomparision(sensaForgotusernamePageObject.lblerrorStreetaddr_sensa, lblstreetaddr);
      if (driver.isIOS) {
        await elementActions.click(sensaAccountPageObject.txtzipCode_sensa);
        await elementActions.click(sensaAccountPageObject.txtcurrentAddr_sensa);
      } else {
        await sensaAccountPageObject.txtzipCode_sensa.doubleClick();
        await sensaAccountPageObject.txtcurrentAddr_sensa.doubleClick();
      }
      await sensaAccountPage.mssgcomparision(sensaForgotusernamePageObject.lblerrorZipCode_sensa, lblzipcode);
      if (driver.isIOS) {
        await elementActions.click(sensaAccountPageObject.txtcity_sensa);
        await elementActions.click(sensaAccountPageObject.txtcurrentAddr_sensa);
      } else {
        await sensaAccountPageObject.txtcity_sensa.doubleClick();
        await sensaAccountPageObject.txtcurrentAddr_sensa.doubleClick();
      }
      await sensaAccountPage.mssgcomparision(sensaForgotusernamePageObject.lblerrorCity_sensa, lblcity);
      (await RegistrationPageObject.txtregemail_sensa).clearValue();
      if (driver.isIOS) {
        await elementActions.click(sensaAccountPageObject.txtzipCode_sensa);
      } else {
        await sensaAccountPageObject.txtzipCode_sensa.doubleClick();
      }
      await sensaAccountPage.mssgcomparision(sensaForgotusernamePageObject.lblerrorEmail_sensa, lblemail);
      if (driver.isIOS) {
        await elementActions.click(sensaAccountPageObject.txtstate_sensa);
        await elementActions.click(sensaAccountPageObject.txtcurrentAddr_sensa);
      } else {
        await elementActions.click(sensaAccountPageObject.txtstate_sensa);
        await RegistrationPageObject.txtmonth_sensa.doubleClick();
      }
      await sensaAccountPage.mssgcomparision(sensaForgotusernamePageObject.lblerrorState_sensa, lblstate);
      if (driver.isIOS) {
        await elementActions.click(RegistrationPageObject.txtmonth_sensa);
        await elementActions.click(RegistrationPageObject.txtday_sensa);
      } else {
        await RegistrationPageObject.txtmonth_sensa.doubleClick();
        await RegistrationPageObject.txtday_sensa.doubleClick();
      }
      await sensaAccountPage.mssgcomparision(sensaForgotusernamePageObject.lblerrorMonth_sensa, lblmonth);
      if (driver.isIOS) {
        await elementActions.click(RegistrationPageObject.txtday_sensa);
        await elementActions.click(RegistrationPageObject.txtmonth_sensa);
      } else {
        await RegistrationPageObject.txtday_sensa.doubleClick();
        await RegistrationPageObject.txtmonth_sensa.doubleClick();
      }
      await sensaAccountPage.mssgcomparision(sensaForgotusernamePageObject.lblerrorDate_sensa, lbldate);
      if (driver.isIOS) {
        await elementActions.click(RegistrationPageObject.txtyear_sensa);
        await elementActions.click(RegistrationPageObject.txtmonth_sensa);
      } else {
        await RegistrationPageObject.txtyear_sensa.doubleClick();
        await RegistrationPageObject.txtmonth_sensa.doubleClick();
      }
      await sensaAccountPage.mssgcomparision(sensaForgotusernamePageObject.lblerrorYear_sensa, lblyear);
      console.log('Validated the Errors in Tellus about page Successfully');
    } catch (error) {
      logger.error('Failed to Validate the Errors in Tellus about page', { error });
      throw error;
    }
  }
  async accountsetuperrorvalidation(filename: string, sheetname: string, scenarioname: string) {
    try {
      const SHEET_NAME = sheetname;
      const jsonFilePath = path.join(process.cwd(), 'data', filename);
      const testData = new JsonTestDataHandler(jsonFilePath);
      const userData1 = testData.getTestCaseData(SHEET_NAME, scenarioname);
      console.log(`User Data for TC01: ${JSON.stringify(userData1)}`);
      const errorPassword = testData.getCellValue(SHEET_NAME, scenarioname, 'lblerrorPassword');
      const lblerrorSecurity = testData.getCellValue(SHEET_NAME, scenarioname, 'lblerrorSecurity');
      await elementActions.click(RegistrationPageObject.txtpassword_sensa);
      await elementActions.click(RegistrationPageObject.txtconfirmPassword_sensa);
      await sensaAccountPage.mssgcomparision(RegistrationPageObject.lblpasswordError_sensa, errorPassword);
      await elementActions.click(sensaRegistrationPageObject.txtsecurityQuestion_sensa);
      await elementActions.click(sensaRegistrationPageObject.txtsecuritynswer_sensa);
      await sensaAccountPage.mssgcomparision(sensaRegistrationPageObject.lblsecurityQuestionError_sensa, lblerrorSecurity);
      await elementActions.click(sensaRegistrationPageObject.txtsecuritynswer_sensa);
      await elementActions.click(sensaRegistrationPageObject.txtsecurityQuestion_sensa);
      await sensaAccountPage.mssgcomparision(sensaRegistrationPageObject.lblsecurityAnswerError_sensa, lblerrorSecurity);



      console.log('Validated the Errors in Account Setup page Successfully');
    } catch (error) {
      logger.error('Failed to Validate the Errors in Account Setup page', { error });
      throw error;
    }
  }

  async passwordFieldErrorValidation(password: string, confirmPassword: string, errormessage: string) {
    try {
      await elementActions.waitForDisplayed(RegistrationPageObject.txtpassword_sensa);
      await elementActions.setValue(RegistrationPageObject.txtpassword_sensa, password);
      await elementActions.setValue(sensaRegistrationPageObject.txtpassword_sensa, password);
      const textbox = await sensaRegistrationPageObject.txtconfirmPassword_sensa;
      await textbox.setValue(confirmPassword);
      await browser.execute((_selector) => {
        const el = document.querySelector('#regConfirmPassword') as HTMLInputElement;
        if (el) {
          el.dispatchEvent(new Event('change', { bubbles: true }));
        }
      }, await sensaRegistrationPageObject.txtconfirmPassword_sensa);
      await elementActions.click(RegistrationPageObject.txtpassword_sensa);
      await sensaAccountPage.mssgcomparision(RegistrationPageObject.lblconfirmpasswordError_sensa, errormessage);
      console.log('Validated Error Message Successfully');
    } catch (error) {
      logger.error('Failed to Validate Error Message', { error });
      throw error;
    }
  }

  async tellUsaboutpageDetailsindcamflow(firstname: string, lastname: string, address: string, zipcode: string, city: string, dob: string) {
    try {
      const [month, day, year] = dob.split('-');
      console.log('Month', month);
      console.log('Year', year);
      console.log('Day', day);

      await elementActions.setValue(RegistrationPageObject.txtFirstName_sensa, firstname);
      await elementActions.setValue(RegistrationPageObject.txtLastName_sensa, lastname);
      await elementActions.setValue(RegistrationPageObject.txtaddress_sensa, address);
      await elementActions.setValue(RegistrationPageObject.txtcity_sensa, city);
      await elementActions.setValue(RegistrationPageObject.txtzipCode_sensa, zipcode);
      const monthdd = await RegistrationPageObject.txtmonth_sensa;
      await monthdd.selectByVisibleText(month);
      const daydd = await RegistrationPageObject.txtday_sensa;
      await daydd.selectByVisibleText(day);
      const yeardd = await RegistrationPageObject.txtyear_sensa;
      await yeardd.selectByVisibleText(year);
      await $('body').click();
      await elementActions.click(RegistrationPageObject.txtweCertify_sensa);
      await elementActions.click(RegistrationPageObject.txtcontinuetoNextStep_sensa);
      console.log('Entered Details Successfully');
    } catch (error) {
      logger.error('Failed to Enter Details', { error });
      throw error;
    }
  }



}
export default new Registration();
