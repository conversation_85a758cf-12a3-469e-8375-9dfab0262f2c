
class sensaStorlocator {

    //StoreLocator
    get hamburgerMenu() { return $('[aria-label="hamburger menu"]'); }
    get sensaStorelocator() { return $('[title="Store Locator"]'); }
    get sensafindastore() { return $('//*[contains(text(),"Find A Store")]'); }
    get sensaUsemyLocation() { return $('//*[contains(text(),"Use My Location")]'); }
    get sensaZipcode() { return $('input[id="zipCode"]'); }
    get sensafindStores() { return $('//*[contains(text(),"Find stores")]'); }
    get sensafilterbyproduct() { return $('(//*[contains(text(),"Filter By Product")])[1]'); }
    get sensa1stStore() { return $('(//*[@class="cmp-store-locator__store-list__stores-store__number"])[1]'); }
    get sensa1stStorename() { return $('(//*[@class="cmp-store-locator__store-list__stores-store__headline"])[1]'); }
    get sensa1stStoredirection() { return $('(//a[contains(text(),"Get Directions")])[1]'); }
    get sensafilterbyproducttext() { return $('//*[contains(text(),"Individual store inventory may vary.")]'); }
    get sensaloadMore() { return $('//*[contains(text(),"Load More")]'); }
    get sensaLogout() { return $('(//*[contains(text(),"Log Out")])[2]'); }

    // Filterproduct
    get btnfilterbyproduct() {return $('//span[text()=\'Filter By Product\']');}
    btnAllproduct() { return $('(//img[@class="cmp-store-locator__filter__img"])'); }
    get closebutton() { return $('//button[@class="cmp-store-locator__filter__modal-close icon-close"]'); }
    btnAllproduct1(i: number) { return $(`(//img[@class='cmp-store-locator__filter__img'])[${i}]`); }
    get btnApplyFilter() { return $('//span[contains(text(),\'Apply Filter\')]/ancestor::button'); }
    get btnfilterproduct() { return $('//button[@class=\'cmp-store-locator__filter__button cmp-button\']'); }
    get labelProduct() {return $('//a[@class=\'cmp-store-locator__filter__text cmp-store-locator__filter__clear-filter-button icon-close\']');}
    get filterDescription_Stores() { return $('//div[@class=\'cmp-store-locator__store-list__description\']');}
    get firstStoreNumber_Stores() { return $('//li[contains(@class,\'cmp-store-locator__store-list__stores-store\')][1]//span[@class=\'cmp-store-locator__store-list__stores-store__number\']');}
    get firstStoreHeader_Stores() {return $('//li[contains(@class,\'cmp-store-locator__store-list__stores-store\')][1]//h4[@class=\'cmp-store-locator__store-list__stores-store__headline\']');}
    get clearFilter() { return $('(//*[text()="Clear Filter"])[2]'); }
    
}
export default new sensaStorlocator();