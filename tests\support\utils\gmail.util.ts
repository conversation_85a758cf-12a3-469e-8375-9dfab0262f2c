import logger from './logger.util.ts';
import '../types/gmail-service.d.ts';

/**
 * Interface for email filter criteria
 */
export interface EmailFilters {
    from?: string;
    to?: string;
    subject?: string;
    includeBody?: boolean;
    includeAttachments?: boolean;
    before?: Date;
    after?: Date;
    label?: string;
}

/**
 * Interface for email object structure
 */
export interface EmailObject {
    from: string;
    to?: string;
    subject: string;
    date: string | Date;
    body?: {
        html?: string;
        text?: string;
    };
    attachments?: Array<{
        filename: string;
        content: string;
        contentType: string;
    }>;
}

interface GmailAuth {
    type?: string;
    user?: string;
    clientId?: string;
    clientSecret?: string;
    refreshToken?: string;
    accessToken?: string;
}

/**
 * Gmail utility class for email operations using wdio-gmail-service
 */
class GmailUtil {
    auth: GmailAuth = {};

    /**
     * Check inbox for emails matching the given criteria
     * @param filters Email filter criteria
     * @returns Array of emails matching the criteria
     */
    public async checkInbox(filters: EmailFilters): Promise<EmailObject[]> {
        try {
            logger.info(`Checking Gmail inbox with filters: ${JSON.stringify(filters)}`);
            // Use the Gmail service provided by wdio-gmail-service
            const emails = await browser.checkInbox(filters);
            // Handle null response from Gmail service
            const emailList = (emails || []) as EmailObject[];
            logger.info(`Found ${emailList.length} emails matching the criteria`);
            return emailList;
        } catch (error) {
            logger.error(`Error checking Gmail inbox: ${error}`);
            throw error;
        }
    }

    /**
     * Wait for an email to arrive with specific criteria
     * @param filters Email filter criteria
     * @param timeoutMs Maximum time to wait in milliseconds
     * @param intervalMs Check interval in milliseconds
     * @returns First email matching the criteria
     */
    /**
     * Utility function to create a delay
     * @param ms Milliseconds to wait
     */
    private async delay(ms: number): Promise<void> {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    public async waitForEmail(
        filters: EmailFilters,
        timeoutMs: number = 60000,
        intervalMs: number = 5000,
    ): Promise<EmailObject> {
        const startTime = Date.now();

        while (Date.now() - startTime < timeoutMs) {
            try {
                const emails = await this.checkInbox(filters);
                if (emails.length > 0) {
                    logger.info(`Email found after ${Date.now() - startTime}ms`);
                    return emails[0];
                }

                logger.info(`No email found yet, waiting ${intervalMs}ms before next check...`);
                await this.delay(intervalMs);
            } catch (error) {
                logger.info(`Error during email check: ${error}`);
                await this.delay(intervalMs);
            }
        }

        throw new Error(`No email found matching criteria after ${timeoutMs}ms timeout`);
    }

    /**
     * Get the latest email from a specific sender
     * @param fromEmail Sender's email address
     * @param includeBody Whether to include email body
     * @returns Latest email from the sender
     */
    public async getLatestEmailFrom(fromEmail: string, includeBody: boolean = true): Promise<EmailObject> {
        logger.info(`Getting latest email from: ${fromEmail}`);

        const emails = await this.checkInbox({
            from: fromEmail,
            includeBody: includeBody,
        });

        if (emails.length === 0) {
            throw new Error(`No emails found from ${fromEmail}`);
        }

        // Sort by date (most recent first) and return the latest
        const sortedEmails = emails.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());
        return sortedEmails[0];
    }

    /**
     * Search for emails containing specific text in subject
     * @param subjectText Text to search for in subject
     * @param includeBody Whether to include email body
     * @returns Array of emails with matching subject
     */
    public async searchEmailsBySubject(subjectText: string, includeBody: boolean = true): Promise<EmailObject[]> {
        logger.info(`Searching emails with subject containing: ${subjectText}`);

        return await this.checkInbox({
            subject: subjectText,
            includeBody: includeBody,
        });
    }

    /**
 * Search for emails containing specific text in subject and return the latest one
 * @param subjectText Text to search for in subject
 * @param includeBody Whether to include email body
 * @returns Latest email with matching subject
 */
    public async searchLatestEmailBySubject(subjectText: string, includeBody: boolean = true): Promise<EmailObject> {
        logger.info(`Searching for latest email with subject containing: ${subjectText}`);

        const emails = await this.checkInbox({
            subject: subjectText,
            includeBody: includeBody,
        });

        if (emails.length === 0) {
            throw new Error(`No emails found with subject containing: ${subjectText}`);
        }

        // Sort by date (most recent first) and return the latest
        const sortedEmails = emails.sort((a, b) => {
            const dateA = new Date(a.date).getTime();
            const dateB = new Date(b.date).getTime();

            // Log for debugging
            logger.info(`Comparing emails: A(${a.date}) vs B(${b.date})`);

            return dateB - dateA; // Most recent first
        });

        const latestEmail = sortedEmails[0];
        logger.info(`Latest email found: from ${latestEmail.from}, date: ${latestEmail.date}, subject: ${latestEmail.subject}`);

        return latestEmail;
    }

    /**
     * Wait for and get the latest email with specific subject (with retry logic)
     * @param subjectText Text to search for in subject
     * @param includeBody Whether to include email body
     * @param timeoutMs Maximum time to wait in milliseconds
     * @param intervalMs Interval between checks in milliseconds
     * @param bufferTimeMs Buffer time in milliseconds to account for email delivery delays (default: 5000ms)
     * @returns Latest email with matching subject that arrived after this function was called
     */
    public async waitForLatestEmailBySubject(
        subjectText: string,
        includeBody: boolean = true,
        timeoutMs: number = 60000,
        intervalMs: number = 3000,
        bufferTimeMs: number = 5000,
    ): Promise<EmailObject> {
        logger.info(`Waiting for latest email with subject containing: ${subjectText}`);

        // Set baseline timestamp with a small buffer to account for email delivery delays
        const baselineTime = new Date(Date.now() - bufferTimeMs);
        logger.info(`Looking for emails received after: ${baselineTime.toISOString()}`);

        const startTime = Date.now();
        let attempts = 0;
        let lastFoundEmail: EmailObject | null = null;
        let stabilityCheckCount = 0;
        const stabilityChecks = 2; // Number of consecutive checks with same latest email before returning

        while (Date.now() - startTime < timeoutMs) {
            attempts++;
            try {
                const emails = await this.checkInbox({
                    subject: subjectText,
                    includeBody: includeBody,
                    after: baselineTime, // Only get emails received after baseline time
                });

                if (emails.length > 0) {
                    // Sort by date (most recent first)
                    const sortedEmails = emails.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());
                    const currentLatest = sortedEmails[0];
                    const currentLatestDate = new Date(currentLatest.date);

                    logger.info(`Found ${emails.length} email(s) after baseline time on attempt ${attempts}`);
                    logger.info(`Current latest email: from ${currentLatest.from}, date: ${currentLatest.date}`);

                    // Check if this is a new email or the same as last found
                    if (!lastFoundEmail || currentLatestDate.getTime() !== new Date(lastFoundEmail.date).getTime()) {
                        // New email found, reset stability counter
                        lastFoundEmail = currentLatest;
                        stabilityCheckCount = 1;
                        logger.info('New email detected, resetting stability counter');
                    } else {
                        // Same email as before, increment stability counter
                        stabilityCheckCount++;
                        logger.info(`Same email found again, stability count: ${stabilityCheckCount}/${stabilityChecks}`);
                    }

                    // If we've seen the same latest email for enough consecutive checks, return it
                    if (stabilityCheckCount >= stabilityChecks) {
                        logger.info(`Email stable for ${stabilityChecks} checks, returning latest email`);
                        logger.info(`Final email: from ${currentLatest.from}, subject: ${currentLatest.subject}, date: ${currentLatest.date}`);
                        return currentLatest;
                    }
                } else {
                    logger.info(`Attempt ${attempts}: No emails found after baseline time, waiting ${intervalMs}ms before next check...`);
                    // Reset stability counter if no emails found
                    stabilityCheckCount = 0;
                    lastFoundEmail = null;
                }

                await this.delay(intervalMs);
            } catch (error) {
                logger.info(`Attempt ${attempts} failed: ${error}`);
                await this.delay(intervalMs);
            }
        }

        // If we found an email but didn't reach stability, return the last found email
        if (lastFoundEmail) {
            logger.info(`Timeout reached but returning last found email: from ${lastFoundEmail.from}, date: ${lastFoundEmail.date}`);
            return lastFoundEmail;
        }

        throw new Error(`No email found with subject containing "${subjectText}" after ${timeoutMs}ms timeout and ${attempts} attempts`);
    }

    /**
     * Wait for a new email with specific subject that arrives after a given timestamp
     * This method is useful when you know exactly when to start looking for emails
     * @param subjectText Text to search for in subject
     * @param afterTimestamp Only look for emails received after this timestamp
     * @param includeBody Whether to include email body
     * @param timeoutMs Maximum time to wait in milliseconds
     * @param intervalMs Interval between checks in milliseconds
     * @returns Latest email with matching subject that arrived after the specified timestamp
     */
    public async waitForNewEmailBySubjectAfterTime(
        subjectText: string,
        afterTimestamp: Date,
        includeBody: boolean = true,
        timeoutMs: number = 60000,
        intervalMs: number = 3000,
    ): Promise<EmailObject> {
        logger.info(`Waiting for new email with subject containing: ${subjectText}`);
        logger.info(`Looking for emails received after: ${afterTimestamp.toISOString()}`);

        const startTime = Date.now();
        let attempts = 0;

        while (Date.now() - startTime < timeoutMs) {
            attempts++;
            try {
                const emails = await this.checkInbox({
                    subject: subjectText,
                    includeBody: includeBody,
                    after: afterTimestamp,
                });

                if (emails.length > 0) {
                    // Sort by date (most recent first) and return the latest
                    const sortedEmails = emails.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());
                    const latestEmail = sortedEmails[0];

                    logger.info(`Found ${emails.length} email(s) after specified time on attempt ${attempts}`);
                    logger.info(`Returning latest email: from ${latestEmail.from}, subject: ${latestEmail.subject}, date: ${latestEmail.date}`);

                    return latestEmail;
                }

                logger.info(`Attempt ${attempts}: No emails found after specified time, waiting ${intervalMs}ms before next check...`);
                await this.delay(intervalMs);
            } catch (error) {
                logger.info(`Attempt ${attempts} failed: ${error}`);
                await this.delay(intervalMs);
            }
        }

        throw new Error(`No email found with subject containing "${subjectText}" after specified timestamp within ${timeoutMs}ms timeout and ${attempts} attempts`);
    }

    /**
     * Verify email content contains expected text
     * @param email Email object
     * @param expectedText Text to search for
     * @param searchInHtml Whether to search in HTML body (default: true)
     * @returns True if text is found
     */
    public verifyEmailContent(email: EmailObject, expectedText: string, searchInHtml: boolean = true): boolean {
        if (!email.body) {
            logger.info('Email body not available for content verification');
            return false;
        }

        const contentToSearch = searchInHtml ? email.body.html : email.body.text;
        const found = Boolean(contentToSearch && contentToSearch.includes(expectedText));

        logger.info(`Email content verification: ${found ? 'PASSED' : 'FAILED'}`);
        logger.info(`Searching for: "${expectedText}"`);

        return found;
    }

    /**
     * Extract links from email HTML body
     * @param email Email object
     * @returns Array of URLs found in the email
     */
    public extractLinksFromEmail(email: EmailObject): string[] {
        if (!email.body || !email.body.html) {
            logger.info('Email HTML body not available for link extraction');
            return [];
        }

        const htmlContent = email.body.html;
        const linkRegex = /href=["'](https?:\/\/[^"']+)["']/gi;
        const links: string[] = [];
        let match;

        while ((match = linkRegex.exec(htmlContent)) !== null) {
            links.push(match[1]);
        }

        logger.info(`Extracted ${links.length} links from email`);
        return links;
    }

    /**
     * Get email count for specific criteria
     * @param filters Email filter criteria
     * @returns Number of emails matching the criteria
     */
    public async getEmailCount(filters: EmailFilters): Promise<number> {
        const emails = await this.checkInbox(filters);
        logger.info(`Email count for given criteria: ${emails.length}`);
        return emails.length;
    }


    /**
    * Click on a link in the email body with specified link text
    * @param email Email object
    * @param linkText Text of the link to click
    */

    public async clickOnLinkInEmail(email: EmailObject, linkText: string): Promise<void> {
        if (!email.body || !email.body.html) {
            logger.info('Email HTML body not available for link extraction');
            console.log('Email HTML body not available for link extraction');
            return;
        }
        const htmlContent = email.body.html;
        // Use a more robust regex to capture the href attribute and link text
        const linkRegex = /<a[^>]*href=["']([^"']+)["'][^>]*>(.*?)<\/a>/gi;
        let match;
        let linkToClick: string | null = null;
        // Search for the link with the specified text
        while ((match = linkRegex.exec(htmlContent)) !== null) {
            const href = match[1].trim();      // The URL
            const anchorText = match[2].trim().toLowerCase(); // The visible text in <a>
            console.log(`Found link: Text="${anchorText}" URL=${href}`);

            if (anchorText.toLowerCase() === (linkText.toLowerCase())) {
                linkToClick = href;
                console.log(`Extracted link successfully: ${linkToClick}`);
                break;
            }

        }
        if (linkToClick) {
            console.log(`Navigating to link: ${linkToClick}`);
            logger.info(`Navigating to link: ${linkToClick}`);
            await browser.url(linkToClick);
        } else {
            logger.info(`No link with text "${linkText}" found in the email.`);
        }


    }

}


export default GmailUtil;
