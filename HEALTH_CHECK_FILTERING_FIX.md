# Health Check URL Filtering Fix

## Issue Identified

**Problem:** Health check URLs and service initialization scenarios were being treated as separate tests in SauceLabs instead of being filtered out.

- Health check URLs like `http://127.0.0.1:8100/health` were appearing as test entries
- Feature names were being used as test names instead of scenario names
- Service initialization was being reported as test execution
- This cluttered the SauceLabs dashboard with non-test entries

## Root Cause

The WebDriverIO configuration was not properly filtering out:
1. **Health Check URLs**: SauceLabs service performs health checks during initialization
2. **Service Initialization Scenarios**: Generic scenarios created during service startup
3. **Empty/Undefined Scenarios**: Scenarios without proper names during service init

## Solution Applied

### 1. Added Health Check URL Detection

**File:** `tests/configs/wdio.shared.conf.ts`

**New Function:**
```typescript
function isHealthCheckOrServiceUrl(url?: string): boolean {
  if (!url) return false;
  
  const healthCheckPatterns = [
    /127\.0\.0\.1:\d+\/health/,
    /localhost:\d+\/health/,
    /\/health$/,
    /\/status$/,
    /\/ping$/,
    /sauce-connect/,
    /saucelabs\.com.*\/health/,
    /appium.*\/health/,
    /webdriver.*\/health/,
  ];
  
  return healthCheckPatterns.some(pattern => pattern.test(url));
}
```

### 2. Added Service Initialization Detection

**New Function:**
```typescript
function isServiceInitializationScenario(scenarioName?: string, featureName?: string): boolean {
  if (!scenarioName && !featureName) return true; // No scenario/feature info = likely service init
  
  // Check for empty or generic scenario names that indicate service initialization
  if (!scenarioName || scenarioName.trim() === '' || scenarioName === 'undefined') {
    return true;
  }
  
  // Check for feature file names that indicate health checks or service initialization
  if (featureName && (
    featureName.toLowerCase().includes('health') ||
    featureName.toLowerCase().includes('service') ||
    featureName.toLowerCase().includes('init') ||
    featureName === 'undefined' ||
    featureName.trim() === ''
  )) {
    return true;
  }
  
  const servicePatterns = [
    /health.*check/i,
    /service.*init/i,
    /sauce.*connect/i,
    /appium.*init/i,
    /webdriver.*init/i,
    /session.*init/i,
    /^test$/i, // Generic "test" scenarios
    /^scenario$/i, // Generic "scenario" scenarios
    /^feature$/i, // Generic "feature" scenarios
  ];
  
  const nameToCheck = `${scenarioName || ''} ${featureName || ''}`.toLowerCase();
  return servicePatterns.some(pattern => pattern.test(nameToCheck));
}
```

### 3. Added Filtering to beforeScenario Hook

**Changes:**
```typescript
beforeScenario: async function (world) {
  const scenarioName = world.pickle.name;
  const featureName = world.pickle.uri?.split('/').pop()?.replace('.feature', '') || 'Unknown Feature';

  // Filter out health checks and service initialization scenarios from SauceLabs reporting
  if (isServiceInitializationScenario(scenarioName, featureName)) {
    console.log(`🔧 Skipping SauceLabs reporting for service initialization: ${scenarioName}`);
    return; // Skip SauceLabs reporting for health checks and service initialization
  }

  // Check if this is a health check URL scenario
  try {
    const currentUrl = await browser.getUrl();
    if (isHealthCheckOrServiceUrl(currentUrl)) {
      console.log(`🔧 Skipping SauceLabs reporting for health check URL: ${currentUrl}`);
      return; // Skip SauceLabs reporting for health check URLs
    }
  } catch {
    // If we can't get URL, continue with normal processing
    console.log('⚠️  Could not check URL for health check filtering, proceeding normally');
  }

  // ... rest of normal scenario processing
}
```

### 4. Added Filtering to afterScenario Hook

**Changes:**
```typescript
afterScenario: async function (world, result) {
  console.log(`Cleaning up after: ${world.pickle.name}`);
  const scenarioName = world.pickle.name;
  const featureName = world.pickle.uri?.split('/').pop()?.replace('.feature', '') || 'Unknown Feature';

  // Filter out health checks and service initialization scenarios from SauceLabs reporting
  if (isServiceInitializationScenario(scenarioName, featureName)) {
    console.log(`🔧 Skipping SauceLabs cleanup for service initialization: ${scenarioName}`);
    return; // Skip SauceLabs cleanup for health checks and service initialization
  }

  // Check if this is a health check URL scenario
  try {
    const currentUrl = await browser.getUrl();
    if (isHealthCheckOrServiceUrl(currentUrl)) {
      console.log(`🔧 Skipping SauceLabs cleanup for health check URL: ${currentUrl}`);
      return; // Skip SauceLabs cleanup for health check URLs
    }
  } catch {
    // If we can't get URL, continue with normal processing
    console.log('⚠️  Could not check URL for health check cleanup filtering, proceeding normally');
  }

  // ... rest of normal scenario cleanup
}
```

## Expected Results

### ✅ **Before Fix:**
- Health check URLs appeared as separate test entries in SauceLabs
- Feature names were used as test names
- Service initialization scenarios cluttered the dashboard
- Multiple non-test entries in SauceLabs reports

### ✅ **After Fix:**
- Health check URLs are filtered out and don't appear in SauceLabs
- Only actual test scenarios appear as test entries
- Clean SauceLabs dashboard with only real test results
- Proper scenario names used as test names

## Verification

To verify the fix is working, check for:

1. **Console Output:**
   - `🔧 Skipping SauceLabs reporting for service initialization: [name]`
   - `🔧 Skipping SauceLabs reporting for health check URL: [url]`

2. **SauceLabs Dashboard:**
   - No health check URLs as test entries
   - No generic "test" or "feature" entries
   - Only actual scenario names appear as tests
   - Clean, focused test reporting

3. **Test Execution:**
   - Normal test scenarios still execute properly
   - Health checks happen naturally without interference
   - Service initialization completes without being reported as tests

## Files Modified

1. **`tests/configs/wdio.shared.conf.ts`**:
   - ✅ Added `isHealthCheckOrServiceUrl()` function
   - ✅ Added `isServiceInitializationScenario()` function
   - ✅ Added filtering to `beforeScenario` hook
   - ✅ Added filtering to `afterScenario` hook

## Benefits

1. **Clean SauceLabs Reports**: Only actual test scenarios appear in reports
2. **Reduced Noise**: No health check or service initialization entries
3. **Better Test Visibility**: Focus on real test results
4. **Proper Test Names**: Scenario names instead of feature names
5. **Service Stability**: Health checks can complete without interference

This fix ensures that SauceLabs reports only contain actual test scenarios, making the dashboard cleaner and more focused on real test results.
