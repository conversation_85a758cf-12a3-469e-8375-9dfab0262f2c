
import { Then, When } from '@wdio/cucumber-framework';
import sensaStorelocatorPage from '../../pages/sensa/sensa-storelocator.page.ts';
import sensaLoginPage from '../../pages/sensa/sensa-login.page.ts';

Then(/^The user click on Store Locator from Hamburger menu$/, async function () {
    await sensaStorelocatorPage.navigateToStorelocatorPage();
});

Then(/^The user click on Use My Location$/, async function () {
    await sensaStorelocatorPage.usemylocation();
});

When(/^The user validate the Store locator page$/, async function () {
    await sensaStorelocatorPage.validateAllStores();
});

When(/^click on Load more and validate more 10 closest stores$/, async function () {
    await sensaStorelocatorPage.validateLoadMoreStores();
});
Then(/^The user enter valid zipcode (.*) and click on Find stores$/, async function(Zipcode : string) {
    await sensaStorelocatorPage.enterZipcode(Zipcode);
});
When(/^I set my location to "([^"]*)" and "([^"]*)"$/, async function (latitude: string, longitude: string) {
    // [Given] Sets up the initial state of the system.
    const lat = parseFloat(latitude);
    const lon = parseFloat(longitude);
    await sensaLoginPage.setLocation(lat, lon);
});
Then(/^The user click on Filter by Product and validate all Product from filter by Product$/, async function () {
    await sensaStorelocatorPage.ClickonFilterbyProductvalidateAll6ProductsFromFilterByProduct();
});

