/**
 * Utility to test and verify retry configuration detection
 * This helps ensure the SauceLabs reporting gets the correct retry count
 */

interface BrowserConfig {
  cucumberOpts?: {
    retry?: number;
  };
}

interface GlobalWithBrowser {
  browser?: {
    config?: BrowserConfig;
  };
}

export class RetryConfigTester {
  /**
   * Get the actual retry configuration from the current config
   * This mirrors the logic in wdio.shared.conf.ts
   */
  static getMaxRetries(): number {
    // Check CLI arguments first
    const cliRetryArg = process.argv.find(arg => arg.startsWith('--retry'));
    if (cliRetryArg) {
      const retryValue = cliRetryArg.split('=')[1] || process.argv[process.argv.indexOf(cliRetryArg) + 1];
      if (retryValue && !isNaN(Number(retryValue))) {
        return Number(retryValue) + 1; // CLI retry is additional attempts, so +1 for total attempts
      }
    }
    
    // Check cucumber options retry setting
    const cucumberRetry = (global as GlobalWithBrowser).browser?.config?.cucumberOpts?.retry;
    if (cucumberRetry !== undefined && !isNaN(Number(cucumberRetry))) {
      return Number(cucumberRetry) + 1; // Cucumber retry is additional attempts, so +1 for total attempts
    }
    
    // Default to 3 total attempts (1 original + 2 retries)
    return 3;
  }

  /**
   * Log current retry configuration for debugging
   */
  static logRetryConfig(): void {
    const maxRetries = this.getMaxRetries();
    console.log('🔧 Retry Configuration Detection:');
    console.log(`   CLI Args: ${process.argv.join(' ')}`);
    console.log(`   Detected Max Retries: ${maxRetries}`);
    
    const cliRetryArg = process.argv.find(arg => arg.startsWith('--retry'));
    if (cliRetryArg) {
      console.log(`   CLI Retry Argument: ${cliRetryArg}`);
    }
    
    const cucumberRetry = (global as GlobalWithBrowser).browser?.config?.cucumberOpts?.retry;
    if (cucumberRetry !== undefined) {
      console.log(`   Cucumber Retry Setting: ${cucumberRetry}`);
    }
  }

  /**
   * Validate that the retry configuration makes sense
   */
  static validateRetryConfig(): boolean {
    const maxRetries = this.getMaxRetries();
    
    if (maxRetries < 1) {
      console.error('❌ Invalid retry configuration: maxRetries must be at least 1');
      return false;
    }
    
    if (maxRetries > 10) {
      console.warn('⚠️  High retry count detected: maxRetries = ' + maxRetries);
    }
    
    console.log('✅ Retry configuration is valid: maxRetries = ' + maxRetries);
    return true;
  }
}
